<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsClinician.php');
include('../class/clsClinicianHospitalSite.php');
include("../class/PasswordHash.php");
include('../class/Zebra_Image.php');
include("../class/class.phpmailer.php");
// include("../class/class.smtp.php");
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include('../class/clsChatApp.php');


if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	//Get Clinician Id and status
	$currentSchoolId = DecodeQueryData($_GET['schoolId']);
	$systemUserId = isset($_GET['id']) ? DecodeQueryData($_GET['id']) : 0;
	$status = ($systemUserId > 0) ? 'updated' : 'added';

	//Get inputs		
	$firstName  = trim($_POST['txtFirstName']);
	$middleName  = trim($_POST['txtMiddleName']);
	$lastName  = trim($_POST['txtLastName']);
	$email  = trim($_POST['txtEmail']);
	$phone  = trim($_POST['txtPhone']);
	$cellPhone  = trim($_POST['txtcellPhone']);
	$address1  = trim($_POST['txtAddress1']);
	$address2  = trim($_POST['txtAddress2']);
	$cboCountry  = trim($_POST['cboCountry']);
	$cbolocation  = trim($_POST['cbolocation']);
	$cbohospitalsites  = $_POST['cbohospitalsites'];
	$city  = trim($_POST['txtCity']);
	$state  = trim($_POST['cboState']);
	$zipCode  = trim($_POST['txtZipCode']);
	$userName = trim($_POST['txtUsername']);
	$cborole  = trim($_POST['cborole']);
	$emailtopassword  = isset($_POST['chkemailtopassword']) ? trim($_POST['chkemailtopassword']) : 0;
	//Check firstName, lastName email and userName blank
	if ($firstName == "" || $lastName == "" || $email == "" || $userName == "") {
		header('location:addclinician.html?status=mandatory&id=' . EncodeQueryData($systemUserId));
		exit();
	}
	$objClinician = new clsClinician();
	//Generate password
	if ($systemUserId == 0 && $emailtopassword == 1) {
		$password = GenerateRandomAlphaNumericNumber(5);
		$objClinician->passwordHash = PasswordHash::hash($password);
	}
	$objSendEmails = new clsSendEmails($currentSchoolId);
	//save data
	$objClinician->locationId = $cbolocation;
	$objClinician->clinicianRoleId = $cborole;
	$objClinician->hospitalSiteId = $cbohospitalsites;
	$objClinician->firstName = ucfirst($firstName);
	$objClinician->middleName = ucfirst($middleName);
	$objClinician->lastName = ucfirst($lastName);
	$objClinician->email = $email;
	$objClinician->phone = $phone;
	$objClinician->cellPhone = $cellPhone;
	$objClinician->address1 = $address1;
	$objClinician->address2 = $address2;
	$objClinician->cboCountry = $cboCountry;
	$objClinician->city = $city;
	$objClinician->stateId = $state;
	$objClinician->zip = $zipCode;
	$objClinician->isActive = '1';
	$objClinician->username = $userName;
	$objClinician->schoolId = $currentSchoolId;
	$objClinician->emailtopassword = $emailtopassword;
	$objClinician->createdBy = $_SESSION['loggedUserId'];
	$retClinicianId = $objClinician->SaveClinician($systemUserId);

	if ($systemUserId == 0 && $emailtopassword == 1) {
		$objSendEmails->SendClinicianLoginDetails($retClinicianId, $password);
		// $objSendEmails->SendPasswordToClinician($password,$retClinicianId,$email,$currenSchoolLogoImagePath,$currenschoolDisplayname);
	}
	if ($retClinicianId > 0) {
		// Handle cropped image from the new image cropper
		$coverImage = isset($_POST['filePhoto']) ? $_POST['filePhoto'] : '';
		if ($coverImage != '') {
			$ext = getFileExtensionFromBase64($coverImage);
			$image_array_1 = explode(";", $coverImage);
			$image_array_2 = explode(",", $image_array_1[1]);
			$coverImage = base64_decode($image_array_2[1]);

			// Check User Directory
			$uploaddir = "../upload/schools/" . $currentSchoolId;
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= "/clinician/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= $retClinicianId . "/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			// File Names
			$smallFilename = 'PROFILE_SMALL_' . $retClinicianId . '.' . $ext;
			$largeFilename = 'PROFILE_LARGE_' . $retClinicianId . '.' . $ext;

			// Save File Paths
			$UploadSmallFilePath = $uploaddir . $smallFilename;
			$UploadLargeFilePath = $uploaddir . $largeFilename;

			// Save cropped image files
			file_put_contents($UploadSmallFilePath, $coverImage);
			file_put_contents($UploadLargeFilePath, $coverImage);

			// Update session variables
			$_SESSION["loggedClinicianProfileImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $smallFilename);
			$_SESSION["loggedClinicianProfileLargeImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $largeFilename);

			// Update File Name to DB
			$objClinician->UpdateClinicianPhotosFileName($retClinicianId, $smallFilename, $largeFilename);
		}
		// Fallback: Handle traditional file upload (for backward compatibility)
		elseif (isset($_FILES['filePhoto']) && !empty($_FILES['filePhoto']['name'])) {
			$Image = $_FILES['filePhoto']['name'];
			$ext = strtolower(pathinfo($_FILES['filePhoto']['name'], PATHINFO_EXTENSION));
			if ($ext != "png" && $ext != "jpg" && $ext != "jpeg" && $ext != "gif") {
				header('location:editprofile.html?status=InvalidFile');
				exit();
			}

			//Check User Directory
			$uploaddir = "../upload/schools/" . $currentSchoolId;
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= "/clinician/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= $retClinicianId . "/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			//Save SMALL Path
			$smallFilename = 'PROFILE_SMALL_' . $retClinicianId . '.' . $ext;
			$UploadSmallFilePath = $uploaddir . $smallFilename;

			if (copy($_FILES['filePhoto']['tmp_name'], $UploadSmallFilePath)) {
				if (isset($_POST['chkAutoCrop'])) {
					$image = new Zebra_Image();
					$image->source_path = $UploadSmallFilePath;
					$image->target_path = $UploadSmallFilePath;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(50, 50, ZEBRA_IMAGE_CROP_CENTER, '-1');
					unset($image);
				}
				$_SESSION["loggedClinicianProfileImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $smallFilename);
			}

			//Save LARGE Path
			$largeFilename = 'PROFILE_LARGE_' . $retClinicianId . '.' . $ext;
			$UploadLargeFilePath = $uploaddir . $largeFilename;
			if (copy($_FILES['filePhoto']['tmp_name'], $UploadLargeFilePath)) {
				if (isset($_POST['chkAutoCrop'])) {
					$image = new Zebra_Image();
					$image->source_path = $UploadLargeFilePath;
					$image->target_path = $UploadLargeFilePath;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, '-1');
					unset($image);
				}
				$_SESSION["loggedClinicianProfileLargeImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $largeFilename);
			}
			//Update File Name to DB
			$objClinician->UpdateClinicianPhotosFileName($retClinicianId, $smallFilename, $largeFilename);
		}

		// Initialize Chat and Database objects
		$objChatApp = new clsChatApp();
		$objDB = new clsDB();

		// Define role ID for the user
		$role_id = 3;

		// Fetch user role management ID and profile image name from the database
		$userRoleManagementId = $objDB->GetSingleColumnValueFromTable('userrolemanagement', 'id', 'userId', $retClinicianId, 'role_Id', $role_id);
		$profileImageName = $objDB->GetSingleColumnValueFromTable('clinician', 'profilePic', 'clinicianId', $retClinicianId);
		// echo $profileImageName;exit;

		// Release database object after use
		unset($objDB);

		// Set default values if necessary
		$userRoleManagementId = $userRoleManagementId ?: 0;
		$profileImagePath = '';

		// Check if profile image exists, then set its path
		if (!empty($profileImageName)) {			
			$profileImagePath = BASE_PATH . '/upload/schools/' . $currentSchoolId . '/clinician/' . $retClinicianId . '/' . $profileImageName . '?id=' . rand(1, 10000);
		}
		// echo $profileImageName;exit;

		// Define a common function to set ChatApp user properties and save the user data
		$retUserRoleManagementId = $objChatApp->setAndSaveChatAppUser($objChatApp, $retClinicianId, $firstName, $lastName, $email, $phone, $address1, $profileImagePath, $currenschoolDisplayname, $currentSchoolId, $role_id, $userRoleManagementId);

		if ($retUserRoleManagementId) {
			$objChatApp->prepareAndSendUserData($objChatApp, $retClinicianId, $firstName, $lastName, $email, $phone, $profileImagePath, $address1, $role_id, $currenschoolDisplayname, $currentSchoolId, $userRoleManagementId);
		}
		if ($userRoleManagementId) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
			$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'address', $address1, 'id', $userRoleManagementId);
			$objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retClinicianId, $role_id);
		}

		//Add hospital sites to clinician
		$objClinicianHospitalSite = new clsClinicianHospitalSite();
		//Delete all clinician hospitalsites
		$objClinicianHospitalSite->DeleteAllClinicianHopitalSites($retClinicianId);
		if (count($cbohospitalsites)) {
			//Now save new sites
			foreach ($cbohospitalsites as $hospitalSiteId) {
				$clinicianHospitalSiteId = $objClinicianHospitalSite->SaveClinicianHopitalSite($retClinicianId, $hospitalSiteId);
			}
		}
		unset($objClinicianHospitalSite);

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($systemUserId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to ADMIN
		$IsMobile = 0;

		$objClinician = new clsClinician();
		$objClinician->saveClinicianAuditLog($retClinicianId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

		unset($objLog);
		//Audit Log Endss

		unset($objClinician);
		header('location:schoolclinicians.html?status=' . $status);
	} else {
		unset($objClinician);
		header('location:addclinician.html?status=error');
	}
} else {
	header('location:addclinician.html');
	exit();
}
