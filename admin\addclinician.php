<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsClinician.php');
include('../class/clsLocations.php');
include('../class/clsHospitalSite.php');
include('../class/clsClinicianRoleMaster.php');
include_once('../class/clsCountryStateMaster.php');
include('../class/clsClinicianHospitalSite.php');


$schoolId = '';
$firstName = '';
$middleName  = '';
$lastName = '';
$address1  = '';
$address2  = '';
$city  = '';
$zip  = '';
$email  = '';
$phone  = '';
$cellPhone  = '';
$username  = '';
$clinicianId  = '0';
$displayName  = '';
$dbStateId = $CurrentSchoolStateId;
$locationId = '';
$hospitalSiteId = '';
$tranSchoolDisplayname = '';
$clinicianHospitalSiteId = '';
$isEmailPassword  = 1;
$clinicianRoleId = '';
$title;
$title = "Add Clinician";
$schoolId = $currentSchoolId;
$tranSchoolDisplayname = $currenschoolDisplayname;
$objSchool = new clsSchool();
$row = $objSchool->GetSchoolDetails($schoolId);
unset($objSchool);
if ($row == '') {
    header('location:addclinician.html');
    exit;
}
//By Default Address is add
$address1  = stripslashes($row['address1']);
$address2  = stripslashes($row['address2']);
$dbStateId  = ($row['stateId']);
$city  = stripslashes($row['city']);
$zip  = stripslashes($row['zip']);
$bedCrumTitle = 'Add';
$profilePic = '';
$arrClinicianHospitalSites = array();
if (isset($_GET['id'])) //Edit Mode
{
    $clinicianId = $_GET['id'];
    $clinicianId = DecodeQueryData($clinicianId);
    $title = "Edit Clinician";
    $bedCrumTitle = 'Edit';
    $objClinician = new clsClinician();
    $rowClinician = $objClinician->GetClinicianDetails($clinicianId);
    $firstName  = stripslashes($rowClinician['firstName']);
    unset($objClinician);
    if ($rowClinician == '') {
        header('location:schoolclinicians.html');
        exit;
    }

    $profilePic = stripslashes($rowClinician['profilePic']);
    $firstName  = stripslashes($rowClinician['firstName']);
    $middleName  = stripslashes($rowClinician['middleName']);
    $lastName  = stripslashes($rowClinician['lastName']);
    $address1  = stripslashes($rowClinician['address1']);
    $address2  = stripslashes($rowClinician['address2']);
    $city  = stripslashes($rowClinician['city']);
    $zip  = stripslashes($rowClinician['zip']);
    $email  = ($rowClinician['email']);
    $phone  = ($rowClinician['phone']);
    $cellPhone  = ($rowClinician['cellPhone']);
    $username  = stripslashes($rowClinician['username']);
    $dbStateId  = ($rowClinician['stateId']);
    $locationId  = ($rowClinician['locationId']);
    $clinicianRoleId  = ($rowClinician['clinicianRoleId']);
    $isEmailPassword  = stripslashes($rowClinician['isEmailPassword']);
    $objClinicianHospitalSite = new clsClinicianHospitalSite();
    $clinicianHospitalsites = $objClinicianHospitalSite->GetClinicianHopitalSites($clinicianId);
    $arrClinicianHospitalSites = GetSingleFieldArrayFromResultSet($clinicianHospitalsites, 'hospitalSiteId');
}



$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
//Read Country From State

$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
unset($objCountryStateMaster);


$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($currentSchoolId);
unset($objLocations);

$objHospitalSite = new clsHospitalSite();
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
unset($objHospitalSite);

$objclinicianrolemaster = new clsClinicianRoleMaster();
$clinicianroles = $objclinicianrolemaster->GetAllClinicianRolesBySchool($currentSchoolId);
unset($objclinicianrolemaster);
$defaultProfileImagePath = GetClinicianImagePath($clinicianId, $currentSchoolId, $profilePic);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .form-control {
            height: 45px;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f9f9 !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            text-wrap: wrap;
        }

        .select2-hidden-accessible~.select2-container--disabled {
            min-height: 45px !important;
            height: fit-content !important;
        }

        .formSubHeading {
            /* margin: 10px 15px 20px; */
        }

        input:read-only {
            background-color: transparent !important;
        }

        .mfp-image-holder .mfp-content {
            background: #fff;
            padding: 10px;
            border-radius: 24px;
            box-shadow: rgb(255 255 255 / 20%) 0px 7px 29px 0px;
        }

        .mfp-image-holder .mfp-close,
        .mfp-iframe-holder .mfp-close {
            color: #000;
            right: 0;
            font-size: 30px !important;
        }

        .mfp-figure:after {
            background: #fff;
            box-shadow: none;
        }

        img.mfp-img {
            /* width: auto; */
            width: 350px;
            height: 350px;
            max-width: 100%;
            /* height: auto; */
            display: block;
            line-height: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            padding: 10px 0;
            margin: 0 auto;
            object-fit: contain;
        }

        .mfp-image-holder .mfp-close,
        .mfp-iframe-holder .mfp-close {
            width: fit-content;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered {
            box-sizing: border-box;
            list-style: none;
            margin: 0;
            padding: 10px 10px;
            width: 100%;
        }

        .select2-container .select2-search--inline .select2-search__field {
            margin-top: 0 !important;
        }
        
        .img-thumbnail{
            padding: 6px;
            border-radius: 13px;
        }

        .formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding:3px 0;
            position:relative;
        }
        input[type="file"] {
            background-color: #fff !important;
        }

        .formSubHeading{
            margin-left: 15px !important;
            margin-right: 15px !important;
            width: -webkit-fill-available;
        }
        
   @media screen and (max-width: 768px) {
    .formSubHeading {
            padding:3px 10px;
        }
   }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schoolclinicians.html">Clinicians</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>
    <div class="container">
        <div class="pageheading"></div>
        <form id="frmClinician" data-parsley-validate class="form-horizontal" method="POST" action="addcliniciansubmit.html?id=<?php echo (EncodeQueryData($clinicianId)); ?>&schoolId=<?php echo (EncodeQueryData($schoolId)); ?>" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-12 formSubHeading">Clinician Information</div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtFirstName">First Name</label>
                        <div class="col-md-12">
                            <input id="txtFirstName" name="txtFirstName" value="<?php echo ($firstName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                            <span id="error" name="error"></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtMiddleName">Middle Name</label>
                        <div class="col-md-12">
                            <input id="txtMiddleName" name="txtMiddleName" value="<?php echo ($middleName); ?>" type="text" placeholder="" class="form-control input-md ">
                            <span id="error" name="error"></span>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtLastName">Last Name</label>
                        <div class="col-md-12">
                            <input id="txtLastName" name="txtLastName" value="<?php echo ($lastName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                            <span id="error" name="error"></span>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtEmail">Email</label>
                        <div class="col-md-12">
                            <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtPhone">Mobile Number</label>
                        <div class="col-md-12">
                            <input id="txtPhone" name="txtPhone" data-inputmask-alias="************" type="text" maxlength="12" placeholder="" value="<?php echo ($phone); ?>" required class="form-control input-md required-input">
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcellPhone">Alternate Phone</label>
                        <div class="col-md-12">
                            <input id="txtcellPhone" name="txtcellPhone" data-inputmask-alias="************" type="text" maxlength="12" value="<?php echo ($cellPhone); ?>" class="form-control">
                        </div>
                    </div>

                </div>
            </div>
            <div class="row">
                <div class="col-md-12 formSubHeading">Address Information</div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                        <div class="col-md-12">
                            <input id="txtAddress1" name="txtAddress1" type="text" placeholder="" value="<?php echo ($address1); ?>" required class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                        <div class="col-md-12">
                            <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboCountry">Country</label>
                        <div class="col-md-12">
                            <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required data-parsley-errors-container="#error-cboCountry">
                                <option value="" selected>Select</option>
                                <?php
                                if ($countries != "") {
                                    while ($row = mysqli_fetch_assoc($countries)) {
                                        $location_id  = $row['location_id'];
                                        $name  = stripslashes($row['name']);
                                ?>
                                        <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php
                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboCountry"></div>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtCity">City</label>
                        <div class="col-md-12">
                            <input id="txtCity" name="txtCity" type="text" placeholder="" value="<?php echo ($city); ?>" required class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboState">State</label>
                        <div class="col-md-12">
                            <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required data-parsley-errors-container="#error-cboState">
                                <option value="" selected>Select</option>
                            </select>
                            <div id="error-cboState"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                        <div class="col-md-12">
                            <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" required class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 formSubHeading">Access Information</div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtUsername">Username</label>
                        <div class="col-md-12">
                            <input id="txtUsername" name="txtUsername" value="<?php echo ($username); ?>" required type="text" placeholder="" class="form-control input-md required-input">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="chkemailtopassword"></label>
                        <div class="col-md-12">
                            <label> <input id="chkemailtopassword" name="chkemailtopassword" value="1" type="checkbox" <?php if ($isEmailPassword) echo 'checked'; ?> class="input-md" onchange="updateEmailToPassword(this)"> Notify login details to clinician.</label>
                        </div>
                    </div>
                    <?php if (isset($_GET['id'])) { ?>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="changepassword"></label>
                            <div class="col-md-12">
                                <a class="addCommentpopup " title="Chnage Password" href="rechangepassword.html?id=<?php echo EncodeQueryData($clinicianId);  ?>">Change Password</a>
                            </div>
                        </div>
                    <?php } ?>
                </div>

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborole">Role</label>
                        <div class="col-md-12">
                            <select id="cborole" name="cborole" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-cborole">
                                <option value="" selected>Select</option>
                                <?php
                                if ($clinicianroles != "") {
                                    while ($row = mysqli_fetch_assoc($clinicianroles)) {
                                        $selclinicianRoleId  = $row['clinicianRoleId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($selclinicianRoleId); ?>" <?php if ($clinicianRoleId == $selclinicianRoleId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cborole"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbolocation">Location</label>
                        <div class="col-md-12">
                            <select id="cbolocation" name="cbolocation" class="form-control input-md required-input select2_single" required>
                                <!--option value="" selected>Select</option---->
                                <?php
                                if ($locations != "") {
                                    while ($row = mysqli_fetch_assoc($locations)) {
                                        $sellocationId  = $row['locationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbohospitalsites">Hospital Sites</label>
                        <div class="col-md-12">
                            <select id="cbohospitalsites" name="cbohospitalsites[]" multiple="multiple" class="form-control input-md  select2_tags" placeholder="Select Hospital site" required data-parsley-errors-container="#error-txtDate">
                                <!-- <option value="" selected>Select</option> -->
                                <?php
                                if ($hospitalSite != "") {
                                    while ($row = mysqli_fetch_assoc($hospitalSite)) {
                                        $selhospitalSiteId  = $row['hospitalSiteId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($selhospitalSiteId); ?>" <?php if (in_array($selhospitalSiteId, $arrClinicianHospitalSites)) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="formSubHeading">Clinician Photo</div>

                    <div style="display: flex; gap: 40px;">
                        <section class="upload-section" id="uploadSection">
                            <div class="upload-area" id="uploadArea">
                                <input style="visibility: hidden;" type="file" id="fileInput" name="filePhoto" accept="image/*" hidden>
                                <input type="hidden" id="filePhoto" name="filePhoto" value="">
                                <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">
                                <div class="upload-content">
                                    <?php
                                    if ($defaultProfileImagePath != '') {
                                    ?>
                                        <img class="img-thumbnail" style="width:100px;height:100px; margin-bottom: 10px;" src="<?php echo ($defaultProfileImagePath . "?randId=" . $randormRefreshId) ?>" alt="" border="0" />
                                        <p>Click to change clinician photo</p>
                                    <?php
                                    } else {
                                    ?>
                                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="7,10 12,15 17,10"></polyline>
                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                        </svg>
                                        <p>Click to browse or drag and drop an image</p>
                                    <?php
                                    }
                                    ?>
                                    <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                                </div>
                            </div>
                        </section>

                        <!-- Cropper Section -->
                        <section class="cropper-section" id="cropperSection" style="display: none;">
                            <div class="controls">
                                <div class="control-group">
                                    <div class="mode-buttons">
                                        <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                                        <button type="button" class="mode-btn" data-mode="square">Square</button>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <button type="button" class="action-btn" id="resetBtn">Reset</button>
                                    <button type="button" class="action-btn" id="backBtn">Back</button>
                                    <button type="button" class="action-btn" id="bgRemovelToggle" style="display: none;">Remove Background</button>
                                    <button type="button" class="action-btn primary" id="cropBtn" style="display: none;">Crop & Save</button>
                                </div>
                                <div class="control-group">
                                    <label>Zoom:</label>
                                    <div class="zoom-controls">
                                        <button type="button" class="zoom-btn" id="zoomOut">-</button>
                                        <span class="zoom-level" id="zoomLevel">100%</span>
                                        <button type="button" class="zoom-btn" id="zoomIn">+</button>
                                        <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 20px; align-items: flex-start;">
                                <div class="image-container" id="imageContainer">
                                    <img id="previewImage" src="" alt="Preview">
                                    <div class="crop-overlay" id="cropOverlay">
                                        <div class="crop-selection" id="cropSelection"></div>
                                    </div>
                                </div>

                                <div class="preview-section">
                                    <h4>Cropped Preview</h4>
                                    <canvas id="previewCanvas" name='previewCanvas'></canvas>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>


            <div class="form-group m-0">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                    <a type="button" href="schoolclinicians.html" class="btn btn-default">Cancel</a>

                </div>
            </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>
    <script type="text/javascript">
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
            new ImageCropper();

            // Check if default clinician image exists and display it
            const defaultImagePath = '<?php echo $defaultProfileImagePath; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');

                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
            }
        });

        //$('#select2-cbohospitalsites-container').addClass('required-select2');
        $('.addCommentpopup').magnificPopup({
            'type': 'ajax'
        });

        <?php if ($clinicianId == 0) { ?>

            $('#txtFirstName, #txtLastName').blur(function() {
                // var autousername = $('#txtFirstName').val() + "<?php echo ($schoolId); ?>";
                var firstname = $('#txtFirstName').val();
                var lastname = $('#txtLastName').val();

                if (firstname && lastname) {
                    // Generate the username using the first letter of the first name and the full last name
                    var firstLetter = firstname.charAt(0);
                    var autousername = (firstLetter + lastname);
                }
                // console.log(autousername);

                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($clinicianId)); ?>',
                        userName: autousername,
                        type: 'clinician',
                        schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            autousername = $('#txtFirstName').val() + "_" + $('#txtLastName').val() + "_" + "<?php echo ($randormRefreshId); ?>";
                            $("#txtUsername").val(autousername);
                        } else {
                            $("#txtUsername").val(autousername);
                        }
                    }
                });


            });
        <?php } ?>

        $("#txtUsername").change(function() {
            var currentUsername = $(this).val();
            $.ajax({
                type: "POST",

                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                data: {
                    userId: '<?php echo (EncodeQueryData($clinicianId)); ?>',
                    userName: currentUsername,
                    type: 'clinician',
                    schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                },
                success: function(responseData) {
                    if (responseData == 1) {
                        alertify.error("Username Not available.");
                        $("#txtUsername").val('').focus();
                    }
                }
            });

        });


        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $(".select2_tags").select2({
                'placeholder': 'Select'
            }); //for multiple selection

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
            $('#select2-cbolocation-container').addClass('required-select2');
            $('#select2-cborank-container').addClass('required-select2');
            $('#select2-cborole-container').addClass('required-select2');
            $('#select2-cbohospitalsites-container').addClass('required-select2');


            /*$(".select2_tags").select2(
            {
              minimumSelectionLength: 2
            });*/



            $('#frmClinician').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            <?php
            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmClinician').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });


            $('.image-preview').magnificPopup({
                type: 'image'
            });

        });
        // $('form').submit(function() {
        // 		var x = document.getElementById("cbohospitalsites").value;
        // 		if (x == "" || x == 0) {  				
        // 		document.getElementById("cbohospitalsites").innerHTML = 'Required';				
        // 		return false;
        // 			}
        // 			else {
        // 				document.getElementById("cbohospitalsites").innerHTML = '';
        // 				return true;
        // 			}
        // 	});

        function updateEmailToPassword(checkbox) {
            var clinicianId = '<?php echo ($clinicianId); ?>'
            var isEmailPassword = (checkbox.checked) ? 1 : 0;
            if (clinicianId > 0) {
                $.ajax({
                    type: "POST",

                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_updateEmailToPassword.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($clinicianId)); ?>',
                        isEmailPassword: isEmailPassword,
                        userType: 'clinician'
                    },
                    success: function(responseData) {
                        // if(responseData==1)
                        // {
                        //     alertify.success("Updated");
                        // }
                    }
                });
            }

        }
    </script>
</body>

</html>